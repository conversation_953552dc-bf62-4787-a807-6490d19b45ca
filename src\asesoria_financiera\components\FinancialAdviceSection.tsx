import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  DollarSign, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle2, 
  Info,
  Target,
  Shield,
  PiggyBank,
  CreditCard
} from 'lucide-react';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { formatCurrency } from '@/components/ui/numeric-input';
import { AdviceDetailModal } from './AdviceDetailModal';

interface AdviceItem {
  id: string;
  title: string;
  description: string;
  type: 'success' | 'warning' | 'danger' | 'info';
  category: 'savings' | 'debt' | 'budget' | 'investment' | 'emergency';
  priority: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendation?: string;
}

export const FinancialAdviceSection: React.FC = () => {
  const { 
    getNetIncome, 
    getTotalExpenses, 
    getNetBalance, 
    getTotalDebt,
    getSavingsRate,
    getDebtToIncomeRatio,
    getEmergencyFundMonths,
    creditCards
  } = useFinanceData();

  const { isMobile } = useBreakpoint();

  const [activeTab, setActiveTab] = useState('all');
  const [selectedAdvice, setSelectedAdvice] = useState<AdviceItem | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleViewDetails = (advice: AdviceItem) => {
    setSelectedAdvice(advice);
    setIsModalOpen(true);
  };

  const generateAdvice = (): AdviceItem[] => {
    const advice: AdviceItem[] = [];
    const netBalance = getNetBalance();
    const savingsRate = getSavingsRate();
    const debtToIncomeRatio = getDebtToIncomeRatio();
    const emergencyFundMonths = getEmergencyFundMonths();

    // Análisis de Tasa de Ahorro
    if (savingsRate < 10) {
      advice.push({
        id: 'low-savings-rate',
        title: 'Tasa de Ahorro Muy Baja',
        description: `Tu tasa de ahorro actual es del ${savingsRate.toFixed(1)}%. Se recomienda ahorrar al menos el 20% de tus ingresos.`,
        type: 'danger',
        category: 'savings',
        priority: 'high',
        actionable: true,
        recommendation: 'Revisa tus gastos y encuentra áreas donde puedas reducir costos para aumentar tu capacidad de ahorro.'
      });
    } else if (savingsRate < 20) {
      advice.push({
        id: 'moderate-savings-rate',
        title: 'Tasa de Ahorro Moderada',
        description: `Tu tasa de ahorro es del ${savingsRate.toFixed(1)}%. Estás en el camino correcto, pero puedes mejorar.`,
        type: 'warning',
        category: 'savings',
        priority: 'medium',
        actionable: true,
        recommendation: 'Intenta aumentar tu tasa de ahorro al 20% o más para acelerar el logro de tus metas financieras.'
      });
    } else {
      advice.push({
        id: 'good-savings-rate',
        title: '¡Excelente Tasa de Ahorro!',
        description: `Tu tasa de ahorro del ${savingsRate.toFixed(1)}% es excelente. Sigue así.`,
        type: 'success',
        category: 'savings',
        priority: 'low',
        actionable: false,
        recommendation: 'Considera invertir tus ahorros para maximizar el crecimiento de tu patrimonio.'
      });
    }

    // Análisis de Fondo de Emergencia
    if (emergencyFundMonths < 3) {
      advice.push({
        id: 'low-emergency-fund',
        title: 'Fondo de Emergencia Insuficiente',
        description: `Tu fondo de emergencia cubre solo ${emergencyFundMonths.toFixed(1)} meses de gastos. Se recomienda tener 3-6 meses.`,
        type: 'danger',
        category: 'emergency',
        priority: 'high',
        actionable: true,
        recommendation: 'Prioriza la creación de un fondo de emergencia antes que otras inversiones.'
      });
    } else if (emergencyFundMonths < 6) {
      advice.push({
        id: 'moderate-emergency-fund',
        title: 'Fondo de Emergencia en Desarrollo',
        description: `Tu fondo de emergencia cubre ${emergencyFundMonths.toFixed(1)} meses de gastos. Está bien, pero puedes mejorarlo.`,
        type: 'warning',
        category: 'emergency',
        priority: 'medium',
        actionable: true,
        recommendation: 'Continúa ahorrando hasta alcanzar 6 meses de gastos en tu fondo de emergencia.'
      });
    } else {
      advice.push({
        id: 'good-emergency-fund',
        title: 'Fondo de Emergencia Sólido',
        description: `Tu fondo de emergencia cubre ${emergencyFundMonths.toFixed(1)} meses de gastos. ¡Excelente!`,
        type: 'success',
        category: 'emergency',
        priority: 'low',
        actionable: false
      });
    }

    // Análisis de Deudas
    if (debtToIncomeRatio > 40) {
      advice.push({
        id: 'high-debt-ratio',
        title: 'Nivel de Deuda Preocupante',
        description: `Tu ratio deuda-ingreso es del ${debtToIncomeRatio.toFixed(1)}%. Esto puede comprometer tu estabilidad financiera.`,
        type: 'danger',
        category: 'debt',
        priority: 'high',
        actionable: true,
        recommendation: 'Considera estrategias de consolidación de deudas o aumentar tus ingresos para reducir este ratio.'
      });
    } else if (debtToIncomeRatio > 20) {
      advice.push({
        id: 'moderate-debt-ratio',
        title: 'Deuda Manageable pero Mejorable',
        description: `Tu ratio deuda-ingreso es del ${debtToIncomeRatio.toFixed(1)}%. Está dentro de límites aceptables.`,
        type: 'warning',
        category: 'debt',
        priority: 'medium',
        actionable: true,
        recommendation: 'Trabaja en reducir tus deudas para liberar más dinero para ahorros e inversiones.'
      });
    } else if (debtToIncomeRatio > 0) {
      advice.push({
        id: 'low-debt-ratio',
        title: 'Buen Manejo de Deudas',
        description: `Tu ratio deuda-ingreso del ${debtToIncomeRatio.toFixed(1)}% está en un nivel saludable.`,
        type: 'success',
        category: 'debt',
        priority: 'low',
        actionable: false
      });
    }

    // Análisis de Tarjetas de Crédito
    const highBalanceCards = creditCards.filter(card => 
      card.isActive && (card.currentBalance / card.creditLimit) > 0.8
    );
    
    if (highBalanceCards.length > 0) {
      advice.push({
        id: 'high-credit-utilization',
        title: 'Alta Utilización de Tarjetas de Crédito',
        description: `Tienes ${highBalanceCards.length} tarjeta(s) con más del 80% de utilización.`,
        type: 'warning',
        category: 'debt',
        priority: 'high',
        actionable: true,
        recommendation: 'Reduce el saldo de tus tarjetas por debajo del 30% del límite para mejorar tu score crediticio.'
      });
    }

    // Análisis de Gastos
    if (netBalance < 0) {
      advice.push({
        id: 'negative-balance',
        title: 'Gastos Superan Ingresos',
        description: `Este mes tus gastos superan tus ingresos por ${formatCurrency(Math.abs(netBalance))}.`,
        type: 'danger',
        category: 'budget',
        priority: 'high',
        actionable: true,
        recommendation: 'Revisa urgentemente tus gastos y elimina los no esenciales para equilibrar tu presupuesto.'
      });
    }

    // Consejos de Inversión
    if (savingsRate > 20 && emergencyFundMonths >= 6 && debtToIncomeRatio < 20) {
      advice.push({
        id: 'ready-to-invest',
        title: 'Listo para Invertir',
        description: 'Tu situación financiera es sólida. Es buen momento para considerar inversiones a largo plazo.',
        type: 'success',
        category: 'investment',
        priority: 'medium',
        actionable: true,
        recommendation: 'Considera diversificar en fondos de inversión, acciones o bienes raíces según tu perfil de riesgo.'
      });
    }

    return advice;
  };

  const advice = generateAdvice();

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle2 className="w-5 h-5 text-green-600" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'danger': return <AlertTriangle className="w-5 h-5 text-red-600" />;
      case 'info': return <Info className="w-5 h-5 text-blue-600" />;
      default: return <Info className="w-5 h-5 text-gray-600" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'savings': return <PiggyBank className="w-4 h-4" />;
      case 'debt': return <CreditCard className="w-4 h-4" />;
      case 'budget': return <DollarSign className="w-4 h-4" />;
      case 'investment': return <TrendingUp className="w-4 h-4" />;
      case 'emergency': return <Shield className="w-4 h-4" />;
      default: return <Target className="w-4 h-4" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high': return <Badge variant="destructive">Alta</Badge>;
      case 'medium': return <Badge variant="secondary">Media</Badge>;
      case 'low': return <Badge variant="outline">Baja</Badge>;
      default: return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const filterAdvice = (category?: string) => {
    if (!category || category === 'all') return advice;
    return advice.filter(item => item.category === category);
  };

  const AdviceList = ({ items }: { items: AdviceItem[] }) => (
    <div className="space-y-4">
      {items.length === 0 ? (
        <div className="text-center py-8">
          <CheckCircle2 className="w-12 h-12 text-green-500 mx-auto mb-3" />
          <p className="text-finanz-text-secondary">No hay consejos en esta categoría</p>
        </div>
      ) : (
        items.map((item) => (
          <Card key={item.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="mt-1">
                  {getTypeIcon(item.type)}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-finanz-text-primary">{item.title}</h4>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1 text-xs text-finanz-text-secondary">
                        {getCategoryIcon(item.category)}
                        <span className="capitalize">{item.category}</span>
                      </div>
                      {getPriorityBadge(item.priority)}
                    </div>
                  </div>
                  
                  <p className="text-finanz-text-secondary mb-3">{item.description}</p>
                  
                  {item.recommendation && (
                    <div className="bg-finanz-neutral/20 rounded-lg p-3 mb-3">
                      <p className="text-sm text-finanz-text-primary">
                        <strong>Recomendación:</strong> {item.recommendation}
                      </p>
                    </div>
                  )}
                  
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleViewDetails(item)}
                  >
                    Ver Detalles
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );

  // Estadísticas financieras para mostrar al inicio
  const financialHealth = () => {
    const netIncome = getNetIncome();
    const savingsRate = getSavingsRate();
    const debtRatio = getDebtToIncomeRatio();
    const emergencyMonths = getEmergencyFundMonths();

    let score = 0;
    if (savingsRate >= 20) score += 25;
    else if (savingsRate >= 10) score += 15;
    
    if (debtRatio <= 20) score += 25;
    else if (debtRatio <= 40) score += 15;
    
    if (emergencyMonths >= 6) score += 25;
    else if (emergencyMonths >= 3) score += 15;
    
    if (netIncome > 0) score += 25;

    return Math.min(score, 100);
  };

  const healthScore = financialHealth();

  const getHealthBarColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    if (score >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const financialData = {
    netIncome: getNetIncome(),
    totalExpenses: getTotalExpenses(),
    totalDebt: getTotalDebt(),
    savingsRate: getSavingsRate(),
    debtToIncomeRatio: getDebtToIncomeRatio(),
    emergencyFundMonths: getEmergencyFundMonths()
  };

  return (
    <div className="space-y-6">
      {/* Financial Health Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-finanz-primary" />
            Puntuación de Salud Financiera
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-3xl font-bold text-finanz-primary">{healthScore}/100</div>
              <p className="text-sm text-finanz-text-secondary">
                {healthScore >= 80 ? 'Excelente' : 
                 healthScore >= 60 ? 'Buena' : 
                 healthScore >= 40 ? 'Regular' : 'Necesita Mejoras'}
              </p>
            </div>
            <Progress
              value={healthScore}
              className="w-1/2"
              indicatorClassName={getHealthBarColor(healthScore)}
            />
          </div>
          <p className="text-sm text-finanz-text-secondary">
            Basado en tu tasa de ahorro, nivel de deudas, fondo de emergencia e ingresos netos.
          </p>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-finanz-text-secondary">Consejos Totales</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl font-bold text-finanz-primary">{advice.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-finanz-text-secondary">Prioridad Alta</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl font-bold text-red-600">
              {advice.filter(a => a.priority === 'high').length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-finanz-text-secondary">Accionables</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl font-bold text-finanz-warning">
              {advice.filter(a => a.actionable).length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-finanz-text-secondary">Aspectos Positivos</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl font-bold text-finanz-success">
              {advice.filter(a => a.type === 'success').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className={`
          grid w-full
          ${isMobile ? 'grid-cols-3 h-auto p-0.5 gap-0.5' : 'grid-cols-6'}
        `}>
          <TabsTrigger
            value="all"
            className={`
              ${isMobile ? 'text-xs py-2 px-1 min-h-[36px] font-medium' : ''}
              transition-all duration-200 touch-manipulation
            `}
          >
            Todos
          </TabsTrigger>
          <TabsTrigger
            value="savings"
            className={`
              ${isMobile ? 'text-xs py-2 px-1 min-h-[36px] font-medium' : ''}
              transition-all duration-200 touch-manipulation
            `}
          >
            Ahorros
          </TabsTrigger>
          <TabsTrigger
            value="debt"
            className={`
              ${isMobile ? 'text-xs py-2 px-1 min-h-[36px] font-medium' : ''}
              transition-all duration-200 touch-manipulation
            `}
          >
            Deudas
          </TabsTrigger>
          {!isMobile && (
            <>
              <TabsTrigger value="budget">Presupuesto</TabsTrigger>
              <TabsTrigger value="emergency">Emergencia</TabsTrigger>
              <TabsTrigger value="investment">Inversión</TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="all">
          <AdviceList items={filterAdvice('all')} />
        </TabsContent>

        <TabsContent value="savings">
          <AdviceList items={filterAdvice('savings')} />
        </TabsContent>

        <TabsContent value="debt">
          <AdviceList items={filterAdvice('debt')} />
        </TabsContent>

        <TabsContent value="budget">
          <AdviceList items={filterAdvice('budget')} />
        </TabsContent>

        <TabsContent value="emergency">
          <AdviceList items={filterAdvice('emergency')} />
        </TabsContent>

        <TabsContent value="investment">
          <AdviceList items={filterAdvice('investment')} />
        </TabsContent>
      </Tabs>

      {/* Modal de Detalles */}
      <AdviceDetailModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        advice={selectedAdvice}
        financialData={financialData}
      />
    </div>
  );
};
